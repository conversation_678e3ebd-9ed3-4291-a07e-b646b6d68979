# Species Diversity 功能完善说明

## 功能概述

已完善了 Species Diversity 页面的创建任务功能逻辑，现在支持完整的参数配置和任务提交。

## 主要改进

### 1. 统一了Species部分
- **Domain选择**: 与Biogeography保持一致，使用 A/B/E/V 作为值
- **Taxonomy选择**: 与Biogeography保持一致，使用 P/C/O/F/G/S 作为值  
- **Species Name**: 添加了多选下拉框，支持最多选择10个物种

### 2. 完善了Data Select部分
- **From Biota模式**: 
  - 使用与Biogeography相同的Water Body Type级联选择器
  - 支持按地理位置(Geolocation)和水体类型(Water Body Type)分类
- **From Cart模式**: 
  - 从购物车选择已保存的分组
  - 显示分组名称和包含的run数量

### 3. 动态分组管理
- 支持添加/删除多个分组
- 每个分组可以独立配置名称和数据来源
- 分组名称自动生成(Group A, Group B, etc.)

### 4. 数据提交逻辑
- 构建符合DTO要求的参数结构
- 根据选择模式正确映射字段:
  - From Biota: 映射到 `waterBodyTypeByClassification` 或 `waterBodyTypeByGeographic`
  - From Cart: 映射到 `runIds`

## API集成

### 新增API函数
```javascript
// 创建Species Diversity任务
export function createSpeciesDiversity(data) {
  return request({
    url: `${baseURL}/speciesDiversity`,
    method: 'post',
    data: data,
  });
}
```

### 提交参数结构
```javascript
{
  domain: "A",           // A/B/E/V
  taxonomy: "P",         // P/C/O/F/G/S  
  speciesNames: ["species1", "species2"],
  groupInfos: [
    {
      groupName: "Group A",
      waterBodyTypeByClassification: "marine",  // From Biota模式
      // 或
      runIds: ["run1", "run2"]                  // From Cart模式
    }
  ]
}
```

## 使用流程

1. **选择Species参数**:
   - 选择Domain (Archaea/Bacteria/Eukaryota/Virus)
   - 选择Taxonomy级别 (Phylum/Class/Order/Family/Genus/Species)
   - 选择1-10个Species Name

2. **配置Data Select**:
   - 选择数据来源模式 (From Biota 或 From Cart)
   - 为每个分组配置:
     - 分组名称
     - 数据来源 (Water Body Type 或 Cart Group)

3. **提交任务**:
   - 系统验证所有必填字段
   - 构建API参数并提交
   - 显示提交结果

## 验证规则 (使用Element Plus表单验证)

### 基础字段验证
- **Domain**: 必选 (A/B/E/V)
- **Taxonomy**: 必选 (P/C/O/F/G/S)
- **Species Name**: 必选，1-10个物种
- **Data Source Type**: 必选 (From Biota/From Cart)

### 分组验证 (自定义验证函数)
- 至少需要1个分组
- 每个分组名称不能为空
- From Biota模式: 必须选择Water Body Type
- From Cart模式: 必须选择购物车分组

### 验证触发时机
- 字段值改变时自动验证
- 添加/删除分组时触发分组验证
- 提交时进行完整表单验证

## 技术实现

- 复用了Biogeography的水体类型数据源和级联选择器
- 集成了购物车Store，实时获取可用分组
- 添加了完整的表单验证和错误提示
- 支持动态添加/删除分组，提供良好的用户体验

## 完成的修改

### 1. API层面 (`app/src/api/diversity.js`)
- 新增 `createSpeciesDiversity` 函数，调用 `/diversity/speciesDiversity` 接口

### 2. 页面层面 (`app/src/views/diversity/index.vue`)
- 统一了Species Diversity和Biogeography的Domain/Taxonomy值格式
- **Species Name与Biogeography共用同一个数据源** (`speciesNameOptions`)
- 完善了From Biota和From Cart两种模式的UI
- 实现了动态分组管理功能
- **使用Element Plus表单验证规则** (`speciesDiversityRules`)
- 实现了`submitSpeciesDiversity`提交函数，使用表单验证替代手动校验
- 添加了`handleGroupWaterBodyTypeChange`和`handleAnalysisTypeChange`处理函数
- 添加了`triggerGroupValidation`函数，在分组变化时触发验证

### 3. 数据结构
- 修改了`form.diversityGroups`的数据结构，简化为只包含必要字段
- 确保与后端DTO (`DiversityCompareCreateDTO`, `GroupInfoDTO`) 完全匹配

## 测试建议

1. 测试Species参数选择和联动
2. 测试From Biota模式的水体类型选择
3. 测试From Cart模式的购物车分组选择
4. 测试动态添加/删除分组功能
5. 测试表单验证规则
6. 测试任务提交流程

功能现已完全实现，可以正常使用！
