<template>
  <!-- Species Section -->
  <el-form-item>
    <template #label>
      <div class="section-label">
        <el-icon color="#0080B0" size="14">
          <Menu />
        </el-icon>
        Species
      </div>
    </template>
    <div class="species-section">
      <!-- Domain Radio Button Group -->
      <el-form-item label="Domain" class="mb-2" prop="domain">
        <el-radio-group
          v-model="form.domain"
          class="custom-radio-buttons"
        >
          <el-radio-button value="A">Archaea</el-radio-button>
          <el-radio-button value="B">Bacteria</el-radio-button>
          <el-radio-button value="E">Eukaryota</el-radio-button>
          <el-radio-button value="V">Virus</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <!-- Taxonomy Radio Button Group -->
      <el-form-item label="Taxonomy" class="mb-2" prop="taxonomy">
        <el-radio-group
          v-model="form.taxonomy"
          class="custom-radio-buttons"
        >
          <el-radio-button value="P">Phylum</el-radio-button>
          <el-radio-button value="C">Class</el-radio-button>
          <el-radio-button value="O">Order</el-radio-button>
          <el-radio-button value="F">Family</el-radio-button>
          <el-radio-button value="G">Genus</el-radio-button>
          <el-radio-button value="S">Species</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <!-- Species Name Multi-select -->
      <el-form-item label="Species Name" class="mb-2" prop="speciesNames">
        <el-select
          v-model="form.speciesNames"
          multiple
          filterable
          :teleported="false"
          placeholder="Select species names"
          class="w-100"
          clearable
          :max-collapse-tags="3"
        >
          <el-option
            v-for="it in speciesNameOptions"
            :key="it.value"
            :label="it.label"
            :value="it.value"
          />
        </el-select>
        <div class="species-warning">
          The maximum number of queried species name is 10
        </div>
      </el-form-item>
    </div>
  </el-form-item>
</template>

<script setup>
import { inject } from 'vue';
import { Menu } from '@element-plus/icons-vue';

// 注入父组件的数据
const form = inject('form');
const speciesNameOptions = inject('speciesNameOptions');
</script>

<style scoped>
.section-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.species-section {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 8px;
}

.custom-radio-buttons {
  width: 100%;
}

.custom-radio-buttons .el-radio-button {
  flex: 1;
}

.species-warning {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.mb-2 {
  margin-bottom: 16px;
}

.w-100 {
  width: 100%;
}
</style>
