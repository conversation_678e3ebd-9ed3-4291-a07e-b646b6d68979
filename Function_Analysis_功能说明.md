# Function Analysis 功能实现说明

## 概述

Function Analysis 是 Genomic 页面的第二个标签页，用于创建功能分析任务。该功能与 Biogeography 共享相同的 Genes Input Type 选择，但在 Data Select 部分有所不同，支持 From Biota 和 From Cart 两种数据源模式。

## 功能特性

### 1. Genes Input Type (与Biogeography共用)
- **Gene Name**: 从下拉框选择基因名称
- **Orthology Entry**: 手动输入KO ID (每行一个)
- **Pathway-KO**: 输入Pathway名称

### 2. Data Select (Function Analysis特有)
- **From Biota**: 通过Water Body Type级联选择器选择数据
- **From Cart**: 从购物车中选择已保存的分组

### 3. 动态分组管理
- 支持添加/删除多个分组
- 每个分组可独立配置数据源
- 分组名称可自定义编辑

## 技术实现

### 1. API层面 (`app/src/api/genomic.js`)
```javascript
// 创建Function Analysis任务
export function createFunctionAnalysis(data) {
  return request({
    url: `${baseURL}/functionAnalysis`,
    method: 'post',
    data: data,
  });
}
```

### 2. 页面层面 (`app/src/views/genomic/index.vue`)
- **表单验证**: 使用Element Plus表单验证规则 (`functionAnalysisRules`)
- **分组管理**: 实现`addGroup`、`removeGroup`、`triggerFunctionGroupValidation`函数
- **提交逻辑**: `submitFunctionAnalysis`函数处理表单验证和数据提交
- **数据共享**: 与Biogeography共用`geneNameOpt`、`waterBodyOpt`、`groupOptions`等数据源

### 3. 后端接口 (`GenomicController.java`)
- **接口路径**: `/genomic/functionAnalysis`
- **请求方法**: POST
- **DTO**: `FunctionCreateDTO`

## 数据流转

### 输入参数映射
根据用户选择的Genes Input Type，参数映射如下：

1. **Gene Name模式**:
   ```javascript
   {
     kos: form.value.geneName,  // 选中的基因名称数组
     pathway: '',
     groupInfos: [...]
   }
   ```

2. **Orthology Entry模式**:
   ```javascript
   {
     kos: form.value.koList.split('\n').filter(ko => ko.trim()),  // KO ID数组
     pathway: '',
     groupInfos: [...]
   }
   ```

3. **Pathway-KO模式**:
   ```javascript
   {
     kos: [],
     pathway: form.value.pathwayKO,  // Pathway名称
     groupInfos: [...]
   }
   ```

### GroupInfoDTO结构
每个分组对应一个`GroupInfoDTO`对象：

```javascript
{
  groupName: "Group A",  // 分组名称
  
  // From Biota模式
  waterBodyTypeByGeographic: "ocean",     // 地理位置类型
  waterBodyTypeByClassification: "lake",  // 分类类型
  
  // From Cart模式  
  runIds: ["run1", "run2", "run3"]  // 购物车分组的runIds
}
```

## 验证规则

### 基础字段验证
- **Genes Input Type**: 必选
- **Gene Name**: Gene Name模式下必选，至少选择一个
- **KO ID**: Orthology Entry模式下必填
- **Pathway Name**: Pathway-KO模式下必填
- **Data Source Type**: 必选 (From Biota/From Cart)

### 分组验证 (自定义验证函数)
- 至少需要1个分组
- 每个分组名称不能为空
- From Biota模式: 必须选择Water Body Type
- From Cart模式: 必须选择购物车分组

### 验证触发时机
- 字段值改变时自动验证
- 添加/删除分组时触发分组验证
- 提交时进行完整表单验证

## 关键函数

### 1. 表单验证
```javascript
const functionAnalysisRules = ref({
  geneType: [{ required: true, message: 'Please select genes input type', trigger: 'change' }],
  geneName: [{ required: true, message: 'Please select at least one gene name', trigger: 'change' }],
  koList: [{ required: true, message: 'Please enter KO ID', trigger: 'blur' }],
  pathwayKO: [{ required: true, message: 'Please enter pathway name', trigger: 'blur' }],
  type: [{ required: true, message: 'Please select data source type', trigger: 'change' }],
  functionGroups: [{ validator: validateFunctionGroups, trigger: 'change' }]
});
```

### 2. 分组管理
```javascript
// 添加分组
function addGroup() {
  const newGroupId = form.value.functionGroups.length + 1;
  const groupLetter = String.fromCharCode(64 + newGroupId);
  
  form.value.functionGroups.push({
    id: newGroupId,
    name: `Group ${groupLetter}`,
    waterBodyType: [],
    selectedGroup: '',
  });
  
  triggerFunctionGroupValidation();
}

// 删除分组
function removeGroup(index) {
  if (form.value.functionGroups.length > 1) {
    form.value.functionGroups.splice(index, 1);
    triggerFunctionGroupValidation();
  }
}
```

### 3. 提交处理
```javascript
function submitFunctionAnalysis() {
  functionAnalysisFormRef.value.validate((valid) => {
    if (!valid) {
      proxy.$modal.msgError('Please fill in all required fields correctly');
      return;
    }
    
    // 构建提交参数并调用API
    const params = { /* ... */ };
    createFunctionAnalysis(params)
      .then(response => {
        proxy.$modal.msgSuccess('Function Analysis submitted successfully');
      })
      .catch(error => {
        proxy.$modal.msgError('Failed to submit Function Analysis');
      });
  });
}
```

## 与Biogeography的差异

1. **共同点**:
   - Genes Input Type选择逻辑完全相同
   - Water Body Type数据源相同
   - 购物车数据源相同

2. **差异点**:
   - Function Analysis支持多分组，Biogeography为单一配置
   - Function Analysis每个分组可独立选择数据源
   - 验证规则和提交逻辑针对多分组场景优化

## 使用流程

1. 选择Genes Input Type (Gene Name/Orthology Entry/Pathway-KO)
2. 根据选择填写对应的基因信息
3. 选择Data Source Type (From Biota/From Cart)
4. 配置至少一个分组的数据源
5. 点击Submit提交任务

## 注意事项

- 确保至少有一个分组且每个分组都正确配置
- From Biota模式下必须选择Water Body Type
- From Cart模式下必须选择购物车中的分组
- 提交前会进行完整的表单验证
